import { Transaction } from '@mysten/sui/transactions';
import { normalizeStructTag } from '@mysten/sui/utils';
import {
  getOwnerCoinsOnchain,
  getReferenceGasPrice,
  getSuiClient,
  suiClient,
  RETRY_MAX_ATTEMPT,
  RETRY_MAX_TIMEOUT,
  RETRY_MIN_TIMEOUT,
} from 'src/utils/suiClient';
import { SUI_TOKEN_ADDRESS_SHORT } from 'src/utils/contants';
import config from 'src/config';
import { TCoinMetadata } from 'src/types';
import BigNumber from 'bignumber.js';
import retry from 'async-retry';
import { DryRunTransactionBlockResponse } from '@mysten/sui/dist/cjs/client';

// Blastfun constants
const BLASTFUN_PACKAGE_ID = '0xb3c504291fc97836d545061c105511a05ea129fb385c6ee8483fc4fff611e236';
const BLASTFUN_MEMEZAV_ID = '0x2319e3e76dfad73d8f4684bdbf42be4f32d8ce4521dd61becc8261dc918d82c0';
const BLASTFUN_MEMEFUN_ID = '0x1fd30fab6dec5f2532dac3eadfae25dab697cd77337d360553a95e81f2a6563c';
const BLASTFUN_BASE_TOKEN = '0xf5663e4a2a9fb1709e5d0c67ad04c8002a50dc48caa65632d52cbd331271e6b7::tmt::TMT';
const BLASTFUN_QUOTE_TOKEN = '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI';

export default class BaseSimulate {
  public buildSponsoredTransaction = async (tx: Transaction) => {
    const sponsorAddress = config.sponsorAddress;
    const coins = await getOwnerCoinsOnchain(sponsorAddress);
    const suiCoins = coins.filter(coin => normalizeStructTag(coin.coinType) === normalizeStructTag(SUI_TOKEN_ADDRESS_SHORT));
    const gasBasePrice = await getReferenceGasPrice();
    tx.setGasOwner(sponsorAddress);
    tx.setGasPrice(gasBasePrice);
    tx.setGasPayment(suiCoins.map(coin => ({
      objectId: coin.coinObjectId,
      version: coin.version,
      digest: coin.digest,
    })));

    return tx;
  }

  public extractTokenX2YFromPoolType = (poolType: string) => {
    const match = poolType.match(/<(.+)>/);
    if (!match) return {};

    const tokens = match[1].split(',').map((t) => t.trim());
    if (tokens.length !== 2) return {};

    return {
      tokenXAddress: tokens[0],
      tokenYAddress: tokens[1],
    };
  };

  /**
   * Creates a buy exact in transaction for Blastfun
   * @param walletAddress - The wallet address to execute the transaction
   * @param exactAmountIn - The exact amount of SUI to spend
   * @param tokenOut - The token metadata for the token to buy
   * @param gasBasePrice - The gas price for the transaction
   * @returns Transaction object ready for simulation or execution
   */
  public createBuyExactInTransaction = (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint,
  ): Transaction => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    // Split coins from gas for the input amount
    const [coin] = tx.splitCoins(tx.gas, [tx.pure.u64(exactAmountIn.toString())]);

    // Make the move call to blastfun_router::buy_exact_in
    tx.moveCall({
      target: `${BLASTFUN_PACKAGE_ID}::blastfun_router::buy_exact_in`,
      typeArguments: [
        BLASTFUN_BASE_TOKEN, // Base token type
        BLASTFUN_QUOTE_TOKEN, // Quote token type (SUI)
      ],
      arguments: [
        tx.object(BLASTFUN_MEMEZAV_ID), // MemezAv object
        tx.object(BLASTFUN_MEMEFUN_ID), // Memefun object
        coin, // Input coin (SUI)
        tx.pure.u64(exactAmountIn.toString()), // Amount in
        tx.pure.u64(0), // Minimum amount out (slippage protection)
        tx.object('0x6'), // Clock object
      ],
    });

    return tx;
  };

  /**
   * Simulates a Blastfun buy transaction and returns the simulation results
   * @param walletAddress - The wallet address to simulate for
   * @param exactAmountIn - The exact amount of SUI to spend
   * @param tokenOut - The token metadata for the token to buy
   * @returns Promise<DryRunTransactionBlockResponse> - The simulation results
   */
  public simulateBlastfunBuy = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
  ): Promise<DryRunTransactionBlockResponse | null> => {
    let client = suiClient;

    try {
      return await retry(
        async () => {
          const gasBasePrice = await getReferenceGasPrice();
          const tx = this.createBuyExactInTransaction(
            walletAddress,
            exactAmountIn,
            tokenOut,
            gasBasePrice,
          );

          const simulateResponse = await client.dryRunTransactionBlock({
            transactionBlock: await tx.build({
              client: client,
            }),
          });

          if (!simulateResponse) {
            throw new Error('Simulate response not found');
          }

          return simulateResponse;
        },
        {
          retries: RETRY_MAX_ATTEMPT,
          minTimeout: RETRY_MIN_TIMEOUT,
          maxTimeout: RETRY_MAX_TIMEOUT,
          onRetry: (e, attempt) => {
            console.log(`simulateBlastfunBuy retry ${attempt}`, e);
            client = getSuiClient(attempt);
          },
        },
      );
    } catch (e) {
      console.error('Failed to simulate Blastfun buy transaction:', e);
      return null;
    }
  };

  /**
   * Extracts the token output amount from simulation events
   * @param simulateResponse - The simulation response from dryRunTransactionBlock
   * @returns string - The amount of tokens that would be received
   */
  public extractTokenOutFromBlastfunSimulation = (
    simulateResponse: DryRunTransactionBlockResponse,
  ): string => {
    try {
      // Look for events that contain token transfer information
      const events = simulateResponse.events || [];

      for (const event of events) {
        // Check for coin creation or transfer events
        if (event.type && event.type.includes('CoinCreated')) {
          const parsedJson = event.parsedJson as any;
          if (parsedJson && parsedJson.amount) {
            return parsedJson.amount.toString();
          }
        }

        // Check for balance change events
        if (event.type && event.type.includes('BalanceChange')) {
          const parsedJson = event.parsedJson as any;
          if (parsedJson && parsedJson.amount && parsedJson.amount > 0) {
            return parsedJson.amount.toString();
          }
        }
      }

      // Fallback: check balance changes in the response
      const balanceChanges = simulateResponse.balanceChanges || [];
      for (const change of balanceChanges) {
        if (change.coinType !== BLASTFUN_QUOTE_TOKEN && BigNumber(change.amount).isPositive()) {
          return change.amount;
        }
      }

      return '0';
    } catch (e) {
      console.error('Failed to extract token output from simulation:', e);
      return '0';
    }
  };
}
